<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import TableDetails from "./components/index.vue";
import { getItemCardsList } from "@/api/coupons/index";

const form = ref<any>({});
const detailsDialogVisible = ref(false);
const isRightType = ref("");
const loading = ref(false);
const isType = ref("");

let formQuery = [
  {
    type: "input",
    key: "categoryName",
    label: "道具卡名称"
  },
  {
    type: "input",
    key: "originalPrice",
    label: "原价"
  },
  {
    type: "input",
    key: "price",
    label: "实际售价"
  }
];
interface Cms {
  property: string;
  label: string;
  width: string;
}
const tableCms: Cms[] = [
  {
    property: "categoryName",
    label: "道具卡名称",
    width: ""
  },
  {
    property: "originalPrice",
    label: "原价",
    width: ""
  },
  {
    property: "price",
    label: "实际售价",
    width: ""
  },
  {
    property: "durationDays",
    label: "有效期",
    width: ""
  }
];
const tableData = ref<any[]>([]);

const currentPage = ref(1);
const pageSize = ref(10);
const size = ref<any>(1);
const background = ref(false);
const disabled = ref(false);
const total = ref(0);
const currentRow = ref<any>({});

const optionsConfig = ref<any[]>([
  {
    label: "待审核",
    value: 0
  },
  {
    label: "通过",
    value: 1
  },
  {
    label: "驳回",
    value: 2
  }
]);

// 分页接口入参
const params = ref({
  entity: {
    originalPrice: null,
    price: null,
    categoryName: ""
  },
  orderBy: {},
  page: 1,
  size: 10
});

const handleSizeChange = val => {
  pageSize.value = val;
  params.value.size = val;
  params.value.page = 1;
  getItemCardsListData();
};
const handleCurrentChange = val => {
  currentPage.value = val;
  params.value.page = val;
  getItemCardsListData();
};

const headerHeight: number = 420;
const tableHeight = ref(window.innerHeight - headerHeight); // 计算高度，headerHeight是你页面顶部栏的高度
function resizeTableHeight() {
  tableHeight.value = window.innerHeight - headerHeight; // 更新表格高度
}

// 详情
const handleDetails = item => {
  currentRow.value = item.row;
  detailsDialogVisible.value = true;
  isType.value = "isDetails";
};

// 编辑
const handleEdit = item => {
  currentRow.value = item.row;
  detailsDialogVisible.value = true;
  isType.value = "isEdit";
};

const handleCancelBtn = () => {
  isRightType.value = "";
  detailsDialogVisible.value = false;
  getItemCardsListData();
};

// 搜索
const handleSearch = () => {
  params.value.entity.categoryName = form.value.categoryName;
  params.value.entity.originalPrice = form.value.originalPrice * 100 || "";
  params.value.entity.price = form.value.price * 100 || "";
  // status 不变
  getItemCardsListData();
};
// 重置
const handleReset = () => {
  form.value = {};
  params.value = {
    entity: {
      originalPrice: null,
      price: null,
      categoryName: ""
    },
    orderBy: {},
    page: 1,
    size: 10
  };
  getItemCardsListData();
};

const getItemCardsListData = async () => {
  loading.value = true;
  try {
    const res: any = await getItemCardsList(params.value);
    if (res.code === 0) {
      total.value = res.total;
      tableData.value = res.data;
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};

onMounted(async () => {
  getItemCardsListData();
  window.addEventListener("resize", resizeTableHeight);
});
onUnmounted(() => {
  window.removeEventListener("resize", resizeTableHeight);
});
</script>

<template>
  <div class="page-container">
    <div class="right-content-container">
      <el-card shadow="never">
        <div class="table-header-flex">
          <el-form :inline="true" :model="form" class="table-header-form">
            <el-form-item
              v-for="(item, index) in formQuery"
              :key="index"
              :label="item.label"
              class="form-item"
            >
              <el-input
                v-if="item.type === 'input'"
                v-model="form[item.key]"
                :placeholder="'请输入' + item.label"
                style="width: 180px"
                clearable
              />
              <el-select
                v-else-if="item.type === 'select'"
                v-model="form[item.key]"
                :placeholder="'请选择' + item.label"
                style="width: 180px"
                clearable
              >
                <el-option
                  v-for="option in optionsConfig"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
              <el-date-picker
                v-else-if="item.type === 'datetime'"
                v-model="form[item.key]"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                style="width: 380px"
                value-format="YYYY-MM-DD HH:mm:ss"
                clearable
              />
            </el-form-item>
          </el-form>
          <div class="form-btns">
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button
              type="info"
              style="background-color: #b4c4d1; color: #ffffff; border: #b4c4d1"
              @click="handleReset"
              >重置</el-button
            >
          </div>
        </div>
      </el-card>

      <el-card shadow="never" style="margin-top: 15px; padding: 5px 15px">
        <el-table
          ref="tableContainer"
          :data="tableData"
          :loading="loading"
          element-loading-text="数据加载中..."
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(255, 255, 255, 0.9)"
          element-loading-svg-view-box="-10, -10, 50, 50"
          style="width: 100%"
          :height="tableHeight"
        >
          <el-table-column
            v-for="(config, index) in tableCms"
            :key="index"
            :width="config.width"
            :label="config.label"
            show-overflow-tooltip
          >
            <template #default="scope">
              <span v-if="config.property === 'originalPrice'">
                {{ `${scope.row[config.property] / 100} 元` }}
              </span>

              <span v-else-if="config.property === 'price'">
                {{ `${scope.row[config.property] / 100} 元` }}
              </span>

              <span v-else-if="config.property === 'durationDays'">
                {{
                  scope.row[config.property] === 0
                    ? "不限时"
                    : `${scope.row[config.property]} 天`
                }}
              </span>

              <span v-else>
                {{ scope.row[config.property] }}
              </span>
            </template>
          </el-table-column>

          <el-table-column fixed="right" label="操作" min-width="120">
            <template #default="scope">
              <el-button
                link
                type="primary"
                style="color: #279efb"
                @click="handleDetails(scope)"
              >
                详情
              </el-button>
              <el-button
                link
                type="primary"
                style="color: #67c23a"
                @click="handleEdit(scope)"
              >
                编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div style="display: flex; justify-content: flex-end; margin-top: 20px">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :size="size"
            :disabled="disabled"
            :background="background"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
      <TableDetails
        v-model:dialogVisible="detailsDialogVisible"
        :currentRow="currentRow"
        :isType="isType"
        @cancelBtn="handleCancelBtn"
      />
    </div>
  </div>
</template>
<style scoped lang="scss">
.billsplit-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 60px;
  line-height: 60px;
}
.table-header {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 5px 20px;
  font-size: 14px;
  button {
    width: 74px !important;
    height: 40px !important;
  }
}
.table-header-row {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  gap: 24px;
  background: #fff;
}
.form-item {
  display: flex;
  align-items: center;
  gap: 8px;
}
.table-header-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}
.table-header-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  flex: 1;
}
.form-item {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}
.form-btns {
  display: flex;
  align-items: center;
  margin-left: 24px;
  gap: 12px;
}

/* 页面布局样式 */
.page-container {
  display: flex;
  align-items: stretch;
  // height: calc(100vh - 84px); // 84px为顶部导航栏高度
  gap: 16px;
}

.right-content-container {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.table-container {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  .add-btn {
    margin-right: 10px;
    color: #fff;
  }
}
</style>
