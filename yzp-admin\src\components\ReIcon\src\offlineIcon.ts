// 这里存放本地图标，在 src/layout/index.vue 文件中加载，避免在首启动加载
import { getSvgInfo } from "@pureadmin/utils";
import { addIcon } from "@iconify/vue/dist/offline";

// https://icon-sets.iconify.design/ep/?keyword=ep
import EpHomeFilled from "~icons/ep/home-filled?raw";

// https://icon-sets.iconify.design/ri/?keyword=ri
import RiSearchLine from "~icons/ri/search-line?raw";
import RiInformationLine from "~icons/ri/information-line?raw";
import RiUserFill from "~icons/ri/user-fill?raw";
import RiBuilding2Fill from "~icons/ri/building-2-fill?raw";
import RiClipboardFill from "~icons/ri/clipboard-fill?raw";
import RiGiftFill from "~icons/ri/gift-fill?raw";
import RiFileTextFill from "~icons/ri/file-text-fill?raw";

const icons = [
  // Element Plus Icon: https://github.com/element-plus/element-plus-icons
  ["ep/home-filled", EpHomeFilled],
  // Remix Icon: https://github.com/Remix-Design/RemixIcon
  ["ri/search-line", RiSearchLine],
  ["ri/information-line", RiInformationLine],
  ["ri-user-fill", RiUserFill],
  ["ri-building-2-fill", RiBuilding2Fill],
  ["ri-clipboard-fill", RiClipboardFill],
  ["ri-gift-fill", RiGiftFill],
  ["ri-file-text-fill", RiFileTextFill]
];

// 本地菜单图标，后端在路由的 icon 中返回对应的图标字符串并且前端在此处使用 addIcon 添加即可渲染菜单图标
icons.forEach(([name, icon]) => {
  addIcon(name as string, getSvgInfo(icon as string));
});
