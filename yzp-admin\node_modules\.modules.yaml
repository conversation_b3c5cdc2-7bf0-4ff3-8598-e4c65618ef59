hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': public
  '@antfu/install-pkg@1.0.0':
    '@antfu/install-pkg': public
  '@antfu/utils@8.1.1':
    '@antfu/utils': public
  '@babel/code-frame@7.26.2':
    '@babel/code-frame': public
  '@babel/compat-data@7.26.8':
    '@babel/compat-data': public
  '@babel/core@7.26.10':
    '@babel/core': public
  '@babel/generator@7.27.0':
    '@babel/generator': public
  '@babel/helper-annotate-as-pure@7.25.9':
    '@babel/helper-annotate-as-pure': public
  '@babel/helper-compilation-targets@7.27.0':
    '@babel/helper-compilation-targets': public
  '@babel/helper-create-class-features-plugin@7.27.0(@babel/core@7.26.10)':
    '@babel/helper-create-class-features-plugin': public
  '@babel/helper-member-expression-to-functions@7.25.9':
    '@babel/helper-member-expression-to-functions': public
  '@babel/helper-module-imports@7.25.9':
    '@babel/helper-module-imports': public
  '@babel/helper-module-transforms@7.26.0(@babel/core@7.26.10)':
    '@babel/helper-module-transforms': public
  '@babel/helper-optimise-call-expression@7.25.9':
    '@babel/helper-optimise-call-expression': public
  '@babel/helper-plugin-utils@7.26.5':
    '@babel/helper-plugin-utils': public
  '@babel/helper-replace-supers@7.26.5(@babel/core@7.26.10)':
    '@babel/helper-replace-supers': public
  '@babel/helper-skip-transparent-expression-wrappers@7.25.9':
    '@babel/helper-skip-transparent-expression-wrappers': public
  '@babel/helper-string-parser@7.25.9':
    '@babel/helper-string-parser': public
  '@babel/helper-validator-identifier@7.25.9':
    '@babel/helper-validator-identifier': public
  '@babel/helper-validator-option@7.25.9':
    '@babel/helper-validator-option': public
  '@babel/helpers@7.27.0':
    '@babel/helpers': public
  '@babel/parser@7.27.0':
    '@babel/parser': public
  '@babel/plugin-syntax-jsx@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-syntax-jsx': public
  '@babel/plugin-syntax-typescript@7.25.9(@babel/core@7.26.10)':
    '@babel/plugin-syntax-typescript': public
  '@babel/plugin-transform-typescript@7.27.0(@babel/core@7.26.10)':
    '@babel/plugin-transform-typescript': public
  '@babel/template@7.27.0':
    '@babel/template': public
  '@babel/traverse@7.27.0':
    '@babel/traverse': public
  '@babel/types@7.27.0':
    '@babel/types': public
  '@commitlint/config-validator@19.8.0':
    '@commitlint/config-validator': public
  '@commitlint/ensure@19.8.0':
    '@commitlint/ensure': public
  '@commitlint/execute-rule@19.8.0':
    '@commitlint/execute-rule': public
  '@commitlint/format@19.8.0':
    '@commitlint/format': public
  '@commitlint/is-ignored@19.8.0':
    '@commitlint/is-ignored': public
  '@commitlint/lint@19.8.0':
    '@commitlint/lint': public
  '@commitlint/load@19.8.0(@types/node@20.17.30)(typescript@5.8.3)':
    '@commitlint/load': public
  '@commitlint/message@19.8.0':
    '@commitlint/message': public
  '@commitlint/parse@19.8.0':
    '@commitlint/parse': public
  '@commitlint/read@19.8.0':
    '@commitlint/read': public
  '@commitlint/resolve-extends@19.8.0':
    '@commitlint/resolve-extends': public
  '@commitlint/rules@19.8.0':
    '@commitlint/rules': public
  '@commitlint/to-lines@19.8.0':
    '@commitlint/to-lines': public
  '@commitlint/top-level@19.8.0':
    '@commitlint/top-level': public
  '@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3)':
    '@csstools/css-parser-algorithms': public
  '@csstools/css-tokenizer@3.0.3':
    '@csstools/css-tokenizer': public
  '@csstools/media-query-list-parser@4.0.2(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)':
    '@csstools/media-query-list-parser': public
  '@csstools/selector-specificity@5.0.0(postcss-selector-parser@7.1.0)':
    '@csstools/selector-specificity': public
  '@ctrl/tinycolor@3.6.1':
    '@ctrl/tinycolor': public
  '@dual-bundle/import-meta-resolve@4.1.0':
    '@dual-bundle/import-meta-resolve': public
  '@element-plus/icons-vue@2.3.1(vue@3.5.13(typescript@5.8.3))':
    '@element-plus/icons-vue': public
  '@esbuild/win32-x64@0.25.3':
    '@esbuild/win32-x64': public
  '@eslint-community/eslint-utils@4.6.1(eslint@9.25.1(jiti@2.4.2))':
    '@eslint-community/eslint-utils': public
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': public
  '@eslint/config-array@0.20.0':
    '@eslint/config-array': public
  '@eslint/config-helpers@0.2.1':
    '@eslint/config-helpers': public
  '@eslint/core@0.13.0':
    '@eslint/core': public
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': public
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': public
  '@eslint/plugin-kit@0.2.8':
    '@eslint/plugin-kit': public
  '@floating-ui/core@1.6.9':
    '@floating-ui/core': public
  '@floating-ui/dom@1.6.13':
    '@floating-ui/dom': public
  '@floating-ui/utils@0.2.9':
    '@floating-ui/utils': public
  '@humanfs/core@0.19.1':
    '@humanfs/core': public
  '@humanfs/node@0.16.6':
    '@humanfs/node': public
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': public
  '@humanwhocodes/retry@0.4.2':
    '@humanwhocodes/retry': public
  '@iconify/types@2.0.0':
    '@iconify/types': public
  '@iconify/utils@2.3.0':
    '@iconify/utils': public
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': public
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': public
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': public
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': public
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': public
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': public
  '@keyv/serialize@1.0.3':
    '@keyv/serialize': public
  '@napi-rs/canvas-win32-x64-msvc@0.1.73':
    '@napi-rs/canvas-win32-x64-msvc': public
  '@napi-rs/canvas@0.1.73':
    '@napi-rs/canvas': public
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': public
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': public
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': public
  '@nuxt/kit@3.16.2':
    '@nuxt/kit': public
  '@parcel/watcher-win32-x64@2.5.1':
    '@parcel/watcher-win32-x64': public
  '@parcel/watcher@2.5.1':
    '@parcel/watcher': public
  '@pkgr/core@0.2.4':
    '@pkgr/core': public
  '@rollup/pluginutils@5.1.4(rollup@4.40.0)':
    '@rollup/pluginutils': public
  '@rollup/rollup-win32-x64-msvc@4.40.0':
    '@rollup/rollup-win32-x64-msvc': public
  '@sindresorhus/merge-streams@2.3.0':
    '@sindresorhus/merge-streams': public
  '@sxzz/popperjs-es@2.11.7':
    '@popperjs/core': public
  '@tailwindcss/node@4.1.4':
    '@tailwindcss/node': public
  '@tailwindcss/oxide-win32-x64-msvc@4.1.4':
    '@tailwindcss/oxide-win32-x64-msvc': public
  '@tailwindcss/oxide@4.1.4':
    '@tailwindcss/oxide': public
  '@trysound/sax@0.2.0':
    '@trysound/sax': public
  '@types/conventional-commits-parser@5.0.1':
    '@types/conventional-commits-parser': public
  '@types/estree@1.0.7':
    '@types/estree': public
  '@types/json-schema@7.0.15':
    '@types/json-schema': public
  '@types/lodash-es@4.17.12':
    '@types/lodash-es': public
  '@types/lodash@4.17.16':
    '@types/lodash': public
  '@types/tinycolor2@1.4.6':
    '@types/tinycolor2': public
  '@types/web-bluetooth@0.0.21':
    '@types/web-bluetooth': public
  '@typescript-eslint/eslint-plugin@8.31.0(@typescript-eslint/parser@8.31.0(eslint@9.25.1(jiti@2.4.2))(typescript@5.8.3))(eslint@9.25.1(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/eslint-plugin': public
  '@typescript-eslint/parser@8.31.0(eslint@9.25.1(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/parser': public
  '@typescript-eslint/scope-manager@8.31.0':
    '@typescript-eslint/scope-manager': public
  '@typescript-eslint/type-utils@8.31.0(eslint@9.25.1(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/type-utils': public
  '@typescript-eslint/types@8.31.0':
    '@typescript-eslint/types': public
  '@typescript-eslint/typescript-estree@8.31.0(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': public
  '@typescript-eslint/utils@8.31.0(eslint@9.25.1(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/utils': public
  '@typescript-eslint/visitor-keys@8.31.0':
    '@typescript-eslint/visitor-keys': public
  '@volar/language-core@2.4.12':
    '@volar/language-core': public
  '@volar/source-map@2.4.12':
    '@volar/source-map': public
  '@volar/typescript@2.4.12':
    '@volar/typescript': public
  '@vue/babel-helper-vue-transform-on@1.4.0':
    '@vue/babel-helper-vue-transform-on': public
  '@vue/babel-plugin-jsx@1.4.0(@babel/core@7.26.10)':
    '@vue/babel-plugin-jsx': public
  '@vue/babel-plugin-resolve-type@1.4.0(@babel/core@7.26.10)':
    '@vue/babel-plugin-resolve-type': public
  '@vue/compiler-core@3.5.13':
    '@vue/compiler-core': public
  '@vue/compiler-dom@3.5.13':
    '@vue/compiler-dom': public
  '@vue/compiler-sfc@3.5.13':
    '@vue/compiler-sfc': public
  '@vue/compiler-ssr@3.5.13':
    '@vue/compiler-ssr': public
  '@vue/compiler-vue2@2.7.16':
    '@vue/compiler-vue2': public
  '@vue/devtools-api@7.7.5':
    '@vue/devtools-api': public
  '@vue/devtools-kit@7.7.5':
    '@vue/devtools-kit': public
  '@vue/devtools-shared@7.7.5':
    '@vue/devtools-shared': public
  '@vue/language-core@2.2.10(typescript@5.8.3)':
    '@vue/language-core': public
  '@vue/reactivity@3.5.13':
    '@vue/reactivity': public
  '@vue/runtime-core@3.5.13':
    '@vue/runtime-core': public
  '@vue/runtime-dom@3.5.13':
    '@vue/runtime-dom': public
  '@vue/server-renderer@3.5.13(vue@3.5.13(typescript@5.8.3))':
    '@vue/server-renderer': public
  '@vue/shared@3.5.13':
    '@vue/shared': public
  '@vueuse/metadata@13.1.0':
    '@vueuse/metadata': public
  '@vueuse/shared@13.1.0(vue@3.5.13(typescript@5.8.3))':
    '@vueuse/shared': public
  JSONStream@1.3.5:
    JSONStream: public
  acorn-jsx@5.3.2(acorn@8.14.1):
    acorn-jsx: public
  acorn@8.14.1:
    acorn: public
  ajv@6.12.6:
    ajv: public
  alien-signals@1.0.13:
    alien-signals: public
  ansi-align@3.0.1:
    ansi-align: public
  ansi-escapes@7.0.0:
    ansi-escapes: public
  ansi-regex@6.1.0:
    ansi-regex: public
  ansi-styles@4.3.0:
    ansi-styles: public
  argparse@2.0.1:
    argparse: public
  array-ify@1.0.0:
    array-ify: public
  array-union@2.1.0:
    array-union: public
  astral-regex@2.0.0:
    astral-regex: public
  async-validator@4.2.5:
    async-validator: public
  async@3.2.6:
    async: public
  asynckit@0.4.0:
    asynckit: public
  balanced-match@2.0.0:
    balanced-match: public
  base64-js@1.5.1:
    base64-js: public
  birpc@2.3.0:
    birpc: public
  boolbase@1.0.0:
    boolbase: public
  brace-expansion@1.1.11:
    brace-expansion: public
  braces@3.0.3:
    braces: public
  browserslist@4.24.4:
    browserslist: public
  buffer@6.0.3:
    buffer: public
  bundle-import@0.0.2:
    bundle-import: public
  c12@3.0.3:
    c12: public
  cacheable@1.8.10:
    cacheable: public
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: public
  call-bound@1.0.4:
    call-bound: public
  callsites@3.1.0:
    callsites: public
  camelcase@8.0.0:
    camelcase: public
  caniuse-api@3.0.0:
    caniuse-api: public
  caniuse-lite@1.0.30001715:
    caniuse-lite: public
  chalk@5.4.1:
    chalk: public
  chokidar@4.0.3:
    chokidar: public
  citty@0.1.6:
    citty: public
  cli-boxes@3.0.0:
    cli-boxes: public
  cli-cursor@5.0.0:
    cli-cursor: public
  cli-truncate@4.0.0:
    cli-truncate: public
  cliui@8.0.1:
    cliui: public
  code-inspector-core@0.20.10:
    code-inspector-core: public
  color-convert@2.0.1:
    color-convert: public
  color-name@1.1.4:
    color-name: public
  colord@2.9.3:
    colord: public
  colorette@2.0.20:
    colorette: public
  combined-stream@1.0.8:
    combined-stream: public
  commander@13.1.0:
    commander: public
  compare-func@2.0.0:
    compare-func: public
  concat-map@0.0.1:
    concat-map: public
  confbox@0.2.2:
    confbox: public
  consola@3.4.2:
    consola: public
  conventional-changelog-angular@7.0.0:
    conventional-changelog-angular: public
  conventional-changelog-conventionalcommits@7.0.2:
    conventional-changelog-conventionalcommits: public
  conventional-commits-parser@5.0.0:
    conventional-commits-parser: public
  convert-source-map@2.0.0:
    convert-source-map: public
  copy-anything@3.0.5:
    copy-anything: public
  cosmiconfig-typescript-loader@6.1.0(@types/node@20.17.30)(cosmiconfig@9.0.0(typescript@5.8.3))(typescript@5.8.3):
    cosmiconfig-typescript-loader: public
  cosmiconfig@9.0.0(typescript@5.8.3):
    cosmiconfig: public
  cross-spawn@7.0.6:
    cross-spawn: public
  css-declaration-sorter@7.2.0(postcss@8.5.3):
    css-declaration-sorter: public
  css-functions-list@3.2.3:
    css-functions-list: public
  css-select@5.1.0:
    css-select: public
  css-tree@3.1.0:
    css-tree: public
  css-what@6.1.0:
    css-what: public
  cssesc@3.0.0:
    cssesc: public
  cssnano-preset-default@7.0.6(postcss@8.5.3):
    cssnano-preset-default: public
  cssnano-utils@5.0.0(postcss@8.5.3):
    cssnano-utils: public
  csso@5.0.5:
    csso: public
  csstype@3.1.3:
    csstype: public
  dargs@8.1.0:
    dargs: public
  de-indent@1.0.2:
    de-indent: public
  debug@4.4.0:
    debug: public
  deep-is@0.1.4:
    deep-is: public
  deep-pick-omit@1.2.1:
    deep-pick-omit: public
  define-lazy-prop@2.0.0:
    define-lazy-prop: public
  defu@6.1.4:
    defu: public
  delayed-stream@1.0.0:
    delayed-stream: public
  destr@2.0.5:
    destr: public
  detect-libc@1.0.3:
    detect-libc: public
  dir-glob@3.0.1:
    dir-glob: public
  dom-serializer@2.0.0:
    dom-serializer: public
  domelementtype@2.3.0:
    domelementtype: public
  domhandler@5.0.3:
    domhandler: public
  domutils@3.2.2:
    domutils: public
  dot-prop@5.3.0:
    dot-prop: public
  dotenv@16.5.0:
    dotenv: public
  dunder-proto@1.0.1:
    dunder-proto: public
  eastasianwidth@0.2.0:
    eastasianwidth: public
  electron-to-chromium@1.5.142:
    electron-to-chromium: public
  emoji-regex@8.0.0:
    emoji-regex: public
  enhanced-resolve@5.18.1:
    enhanced-resolve: public
  entities@4.5.0:
    entities: public
  env-paths@2.2.1:
    env-paths: public
  environment@1.1.0:
    environment: public
  error-ex@1.3.2:
    error-ex: public
  errx@0.1.0:
    errx: public
  es-define-property@1.0.1:
    es-define-property: public
  es-errors@1.3.0:
    es-errors: public
  es-module-lexer@0.4.1:
    es-module-lexer: public
  es-object-atoms@1.1.1:
    es-object-atoms: public
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: public
  esbuild-code-inspector-plugin@0.20.10:
    esbuild-code-inspector-plugin: public
  esbuild@0.25.3:
    esbuild: public
  escalade@3.2.0:
    escalade: public
  escape-html@1.0.3:
    escape-html: public
  escape-string-regexp@4.0.0:
    escape-string-regexp: public
  eslint-scope@8.3.0:
    eslint-scope: public
  eslint-visitor-keys@4.2.0:
    eslint-visitor-keys: public
  espree@10.3.0:
    espree: public
  esquery@1.6.0:
    esquery: public
  esrecurse@4.3.0:
    esrecurse: public
  estraverse@5.3.0:
    estraverse: public
  estree-walker@2.0.2:
    estree-walker: public
  esutils@2.0.3:
    esutils: public
  eventemitter3@5.0.1:
    eventemitter3: public
  execa@8.0.1:
    execa: public
  exsolve@1.0.5:
    exsolve: public
  fast-deep-equal@3.1.3:
    fast-deep-equal: public
  fast-diff@1.3.0:
    fast-diff: public
  fast-glob@3.3.3:
    fast-glob: public
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: public
  fast-levenshtein@2.0.6:
    fast-levenshtein: public
  fast-uri@3.0.6:
    fast-uri: public
  fastest-levenshtein@1.0.16:
    fastest-levenshtein: public
  fastq@1.19.1:
    fastq: public
  fdir@6.4.4(picomatch@4.0.2):
    fdir: public
  file-entry-cache@8.0.0:
    file-entry-cache: public
  fill-range@7.1.1:
    fill-range: public
  find-up@5.0.0:
    find-up: public
  flat-cache@6.1.8:
    flat-cache: public
  flatted@3.3.3:
    flatted: public
  follow-redirects@1.15.9:
    follow-redirects: public
  foreground-child@3.3.1:
    foreground-child: public
  form-data@4.0.2:
    form-data: public
  framesync@6.1.2:
    framesync: public
  fs-extra@10.1.0:
    fs-extra: public
  function-bind@1.1.2:
    function-bind: public
  gensync@1.0.0-beta.2:
    gensync: public
  get-caller-file@2.0.5:
    get-caller-file: public
  get-east-asian-width@1.3.0:
    get-east-asian-width: public
  get-intrinsic@1.3.0:
    get-intrinsic: public
  get-proto@1.0.1:
    get-proto: public
  get-stream@8.0.1:
    get-stream: public
  get-tsconfig@4.10.0:
    get-tsconfig: public
  giget@2.0.0:
    giget: public
  git-raw-commits@4.0.0:
    git-raw-commits: public
  glob-parent@6.0.2:
    glob-parent: public
  glob@11.0.2:
    glob: public
  global-directory@4.0.1:
    global-directory: public
  global-modules@2.0.0:
    global-modules: public
  global-prefix@3.0.0:
    global-prefix: public
  globals@14.0.0:
    globals: public
  globby@11.1.0:
    globby: public
  globjoin@0.1.4:
    globjoin: public
  gopd@1.2.0:
    gopd: public
  graceful-fs@4.2.11:
    graceful-fs: public
  graphemer@1.4.0:
    graphemer: public
  has-flag@4.0.0:
    has-flag: public
  has-symbols@1.1.0:
    has-symbols: public
  has-tostringtag@1.0.2:
    has-tostringtag: public
  hasown@2.0.2:
    hasown: public
  he@1.2.0:
    he: public
  hey-listen@1.0.8:
    hey-listen: public
  hookable@5.5.3:
    hookable: public
  hookified@1.8.2:
    hookified: public
  html-tags@3.3.1:
    html-tags: public
  htmlparser2@8.0.2:
    htmlparser2: public
  human-signals@5.0.0:
    human-signals: public
  ieee754@1.2.1:
    ieee754: public
  ignore@5.3.2:
    ignore: public
  immediate@3.0.6:
    immediate: public
  immutable@5.1.1:
    immutable: public
  import-fresh@3.3.1:
    import-fresh: public
  import-from-string@0.0.5:
    import-from-string: public
  import-meta-resolve@4.1.0:
    import-meta-resolve: public
  imurmurhash@0.1.4:
    imurmurhash: public
  ini@1.3.8:
    ini: public
  is-arrayish@0.2.1:
    is-arrayish: public
  is-docker@2.2.1:
    is-docker: public
  is-extglob@2.1.1:
    is-extglob: public
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: public
  is-glob@4.0.3:
    is-glob: public
  is-number@7.0.0:
    is-number: public
  is-obj@2.0.0:
    is-obj: public
  is-plain-object@5.0.0:
    is-plain-object: public
  is-reference@3.0.3:
    is-reference: public
  is-stream@3.0.0:
    is-stream: public
  is-text-path@2.0.0:
    is-text-path: public
  is-what@4.1.16:
    is-what: public
  is-wsl@2.2.0:
    is-wsl: public
  isexe@2.0.0:
    isexe: public
  jackspeak@4.1.0:
    jackspeak: public
  jiti@2.4.2:
    jiti: public
  js-tokens@9.0.1:
    js-tokens: public
  js-yaml@4.1.0:
    js-yaml: public
  jsbn@1.1.0:
    jsbn: public
  jsesc@3.1.0:
    jsesc: public
  json-buffer@3.0.1:
    json-buffer: public
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: public
  json-schema-traverse@0.4.1:
    json-schema-traverse: public
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: public
  json5@2.2.3:
    json5: public
  jsonfile@6.1.0:
    jsonfile: public
  jsonparse@1.3.1:
    jsonparse: public
  keyv@4.5.4:
    keyv: public
  kind-of@6.0.3:
    kind-of: public
  klona@2.0.6:
    klona: public
  knitwork@1.2.0:
    knitwork: public
  known-css-properties@0.36.0:
    known-css-properties: public
  kolorist@1.8.0:
    kolorist: public
  launch-ide@1.0.7:
    launch-ide: public
  levn@0.4.1:
    levn: public
  lie@3.1.1:
    lie: public
  lightningcss-win32-x64-msvc@1.29.2:
    lightningcss-win32-x64-msvc: public
  lightningcss@1.29.2:
    lightningcss: public
  lilconfig@3.1.3:
    lilconfig: public
  lines-and-columns@1.2.4:
    lines-and-columns: public
  listr2@8.3.2:
    listr2: public
  local-pkg@1.1.1:
    local-pkg: public
  locate-path@6.0.0:
    locate-path: public
  lodash-es@4.17.21:
    lodash-es: public
  lodash-unified@1.0.3(@types/lodash-es@4.17.12)(lodash-es@4.17.21)(lodash@4.17.21):
    lodash-unified: public
  lodash.camelcase@4.3.0:
    lodash.camelcase: public
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: public
  lodash.kebabcase@4.1.1:
    lodash.kebabcase: public
  lodash.memoize@4.1.2:
    lodash.memoize: public
  lodash.merge@4.6.2:
    lodash.merge: public
  lodash.mergewith@4.6.2:
    lodash.mergewith: public
  lodash.snakecase@4.1.1:
    lodash.snakecase: public
  lodash.startcase@4.4.0:
    lodash.startcase: public
  lodash.truncate@4.4.2:
    lodash.truncate: public
  lodash.uniq@4.5.0:
    lodash.uniq: public
  lodash.upperfirst@4.3.1:
    lodash.upperfirst: public
  lodash@4.17.21:
    lodash: public
  log-update@6.1.0:
    log-update: public
  lru-cache@5.1.1:
    lru-cache: public
  magic-string@0.30.17:
    magic-string: public
  math-intrinsics@1.1.0:
    math-intrinsics: public
  mathml-tag-names@2.1.3:
    mathml-tag-names: public
  mdn-data@2.0.30:
    mdn-data: public
  memoize-one@6.0.0:
    memoize-one: public
  meow@13.2.0:
    meow: public
  merge-stream@2.0.0:
    merge-stream: public
  merge2@1.4.1:
    merge2: public
  micromatch@4.0.8:
    micromatch: public
  mime-db@1.52.0:
    mime-db: public
  mime-types@2.1.35:
    mime-types: public
  mimic-fn@4.0.0:
    mimic-fn: public
  mimic-function@5.0.1:
    mimic-function: public
  minimatch@3.1.2:
    minimatch: public
  minimist@1.2.8:
    minimist: public
  minipass@7.1.2:
    minipass: public
  mlly@1.7.4:
    mlly: public
  ms@2.1.3:
    ms: public
  muggle-string@0.4.1:
    muggle-string: public
  nanoid@3.3.11:
    nanoid: public
  natural-compare@1.4.0:
    natural-compare: public
  node-addon-api@7.1.1:
    node-addon-api: public
  node-fetch-native@1.6.6:
    node-fetch-native: public
  node-releases@2.0.19:
    node-releases: public
  normalize-path@3.0.0:
    normalize-path: public
  normalize-wheel-es@1.2.0:
    normalize-wheel-es: public
  npm-run-path@5.3.0:
    npm-run-path: public
  nth-check@2.1.1:
    nth-check: public
  nypm@0.6.0:
    nypm: public
  object-inspect@1.13.4:
    object-inspect: public
  ohash@2.0.11:
    ohash: public
  onetime@6.0.0:
    onetime: public
  open@8.4.2:
    open: public
  optionator@0.9.4:
    optionator: public
  p-limit@3.1.0:
    p-limit: public
  p-locate@5.0.0:
    p-locate: public
  package-json-from-dist@1.0.1:
    package-json-from-dist: public
  package-manager-detector@0.2.11:
    package-manager-detector: public
  parent-module@1.0.1:
    parent-module: public
  parse-json@5.2.0:
    parse-json: public
  path-exists@4.0.0:
    path-exists: public
  path-key@3.1.1:
    path-key: public
  path-scurry@2.0.0:
    path-scurry: public
  path-to-regexp@8.2.0:
    path-to-regexp: public
  path-type@4.0.0:
    path-type: public
  pathe@1.1.2:
    pathe: public
  pdfjs-dist@4.10.38:
    pdfjs-dist: public
  perfect-debounce@1.0.0:
    perfect-debounce: public
  picocolors@1.1.1:
    picocolors: public
  picomatch@4.0.2:
    picomatch: public
  pidtree@0.6.0:
    pidtree: public
  pkg-types@2.1.0:
    pkg-types: public
  popmotion@11.0.5:
    popmotion: public
  portfinder@1.0.36:
    portfinder: public
  postcss-calc@10.1.1(postcss@8.5.3):
    postcss-calc: public
  postcss-colormin@7.0.2(postcss@8.5.3):
    postcss-colormin: public
  postcss-convert-values@7.0.4(postcss@8.5.3):
    postcss-convert-values: public
  postcss-discard-comments@7.0.3(postcss@8.5.3):
    postcss-discard-comments: public
  postcss-discard-duplicates@7.0.1(postcss@8.5.3):
    postcss-discard-duplicates: public
  postcss-discard-empty@7.0.0(postcss@8.5.3):
    postcss-discard-empty: public
  postcss-discard-overridden@7.0.0(postcss@8.5.3):
    postcss-discard-overridden: public
  postcss-media-query-parser@0.2.3:
    postcss-media-query-parser: public
  postcss-merge-longhand@7.0.4(postcss@8.5.3):
    postcss-merge-longhand: public
  postcss-merge-rules@7.0.4(postcss@8.5.3):
    postcss-merge-rules: public
  postcss-minify-font-values@7.0.0(postcss@8.5.3):
    postcss-minify-font-values: public
  postcss-minify-gradients@7.0.0(postcss@8.5.3):
    postcss-minify-gradients: public
  postcss-minify-params@7.0.2(postcss@8.5.3):
    postcss-minify-params: public
  postcss-minify-selectors@7.0.4(postcss@8.5.3):
    postcss-minify-selectors: public
  postcss-normalize-charset@7.0.0(postcss@8.5.3):
    postcss-normalize-charset: public
  postcss-normalize-display-values@7.0.0(postcss@8.5.3):
    postcss-normalize-display-values: public
  postcss-normalize-positions@7.0.0(postcss@8.5.3):
    postcss-normalize-positions: public
  postcss-normalize-repeat-style@7.0.0(postcss@8.5.3):
    postcss-normalize-repeat-style: public
  postcss-normalize-string@7.0.0(postcss@8.5.3):
    postcss-normalize-string: public
  postcss-normalize-timing-functions@7.0.0(postcss@8.5.3):
    postcss-normalize-timing-functions: public
  postcss-normalize-unicode@7.0.2(postcss@8.5.3):
    postcss-normalize-unicode: public
  postcss-normalize-url@7.0.0(postcss@8.5.3):
    postcss-normalize-url: public
  postcss-normalize-whitespace@7.0.0(postcss@8.5.3):
    postcss-normalize-whitespace: public
  postcss-ordered-values@7.0.1(postcss@8.5.3):
    postcss-ordered-values: public
  postcss-reduce-initial@7.0.2(postcss@8.5.3):
    postcss-reduce-initial: public
  postcss-reduce-transforms@7.0.0(postcss@8.5.3):
    postcss-reduce-transforms: public
  postcss-resolve-nested-selector@0.1.6:
    postcss-resolve-nested-selector: public
  postcss-safe-parser@6.0.0(postcss@8.5.3):
    postcss-safe-parser: public
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: public
  postcss-sorting@8.0.2(postcss@8.5.3):
    postcss-sorting: public
  postcss-svgo@7.0.1(postcss@8.5.3):
    postcss-svgo: public
  postcss-unique-selectors@7.0.3(postcss@8.5.3):
    postcss-unique-selectors: public
  postcss-value-parser@4.2.0:
    postcss-value-parser: public
  prelude-ls@1.2.1:
    prelude-ls: public
  prettier-linter-helpers@1.0.0:
    prettier-linter-helpers: public
  proxy-from-env@1.1.0:
    proxy-from-env: public
  punycode@2.3.1:
    punycode: public
  quansync@0.2.10:
    quansync: public
  queue-microtask@1.2.3:
    queue-microtask: public
  rc9@2.1.2:
    rc9: public
  readdirp@4.1.2:
    readdirp: public
  require-directory@2.1.1:
    require-directory: public
  require-from-string@2.0.2:
    require-from-string: public
  resolve-from@5.0.0:
    resolve-from: public
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: public
  restore-cursor@5.1.0:
    restore-cursor: public
  reusify@1.1.0:
    reusify: public
  rfdc@1.4.1:
    rfdc: public
  rollup-plugin-external-globals@0.10.0(rollup@4.40.0):
    rollup-plugin-external-globals: public
  rollup@4.40.0:
    rollup: public
  run-parallel@1.2.0:
    run-parallel: public
  scule@1.3.0:
    scule: public
  semver@7.7.1:
    semver: public
  shebang-command@2.0.0:
    shebang-command: public
  shebang-regex@3.0.0:
    shebang-regex: public
  side-channel-list@1.0.0:
    side-channel-list: public
  side-channel-map@1.0.1:
    side-channel-map: public
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: public
  side-channel@1.1.0:
    side-channel: public
  signal-exit@4.1.0:
    signal-exit: public
  slash@3.0.0:
    slash: public
  slice-ansi@4.0.0:
    slice-ansi: public
  source-map-js@1.2.1:
    source-map-js: public
  source-map@0.7.4:
    source-map: public
  sourcemap-codec@1.4.8:
    sourcemap-codec: public
  speakingurl@14.0.1:
    speakingurl: public
  split2@4.2.0:
    split2: public
  std-env@3.9.0:
    std-env: public
  string-argv@0.3.2:
    string-argv: public
  string-width@4.2.3:
    string-width-cjs: public
  string-width@7.2.0:
    string-width: public
  strip-ansi@6.0.1:
    strip-ansi: public
    strip-ansi-cjs: public
  strip-final-newline@3.0.0:
    strip-final-newline: public
  strip-json-comments@3.1.1:
    strip-json-comments: public
  strip-literal@3.0.0:
    strip-literal: public
  style-value-types@5.1.2:
    style-value-types: public
  stylehacks@7.0.4(postcss@8.5.3):
    stylehacks: public
  stylelint-config-html@1.1.0(postcss-html@1.8.0)(stylelint@16.19.0(typescript@5.8.3)):
    stylelint-config-html: public
  stylelint-config-recommended-scss@14.1.0(postcss@8.5.3)(stylelint@16.19.0(typescript@5.8.3)):
    stylelint-config-recommended-scss: public
  stylelint-config-recommended@16.0.0(stylelint@16.19.0(typescript@5.8.3)):
    stylelint-config-recommended: public
  stylelint-config-standard@36.0.1(stylelint@16.19.0(typescript@5.8.3)):
    stylelint-config-standard: public
  stylelint-order@6.0.4(stylelint@16.19.0(typescript@5.8.3)):
    stylelint-order: public
  stylelint-scss@6.11.1(stylelint@16.19.0(typescript@5.8.3)):
    stylelint-scss: public
  superjson@2.2.2:
    superjson: public
  supports-color@7.2.0:
    supports-color: public
  supports-hyperlinks@3.2.0:
    supports-hyperlinks: public
  svg-tags@1.0.0:
    svg-tags: public
  synckit@0.11.4:
    synckit: public
  table@6.9.0:
    table: public
  tapable@2.2.1:
    tapable: public
  text-extensions@2.4.0:
    text-extensions: public
  through@2.3.8:
    through: public
  tinycolor2@1.6.0:
    tinycolor2: public
  tinyexec@0.3.2:
    tinyexec: public
  tinyglobby@0.2.13:
    tinyglobby: public
  tinygradient@1.1.5:
    tinygradient: public
  tippy.js@6.3.7:
    tippy.js: public
  to-regex-range@5.0.1:
    to-regex-range: public
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: public
  tslib@2.3.0:
    tslib: public
  type-check@0.4.0:
    type-check: public
  type-fest@4.40.0:
    type-fest: public
  ufo@1.6.1:
    ufo: public
  unctx@2.4.1:
    unctx: public
  undici-types@6.19.8:
    undici-types: public
  unicorn-magic@0.3.0:
    unicorn-magic: public
  unimport@4.2.0:
    unimport: public
  universalify@2.0.1:
    universalify: public
  unplugin-utils@0.2.4:
    unplugin-utils: public
  unplugin@2.3.2:
    unplugin: public
  untyped@2.0.0:
    untyped: public
  update-browserslist-db@1.1.3(browserslist@4.24.4):
    update-browserslist-db: public
  uri-js@4.4.1:
    uri-js: public
  util-deprecate@1.0.2:
    util-deprecate: public
  vite-code-inspector-plugin@0.20.10:
    vite-code-inspector-plugin: public
  vite-plugin-externals@0.6.2(vite@6.3.3(@types/node@20.17.30)(jiti@2.4.2)(lightningcss@1.29.2)(sass@1.87.0)(yaml@2.7.1)):
    vite-plugin-externals: public
  vscode-uri@3.1.0:
    vscode-uri: public
  vue-demi@0.14.10(vue@3.5.13(typescript@5.8.3)):
    vue-demi: public
  webpack-code-inspector-plugin@0.20.10:
    webpack-code-inspector-plugin: public
  webpack-virtual-modules@0.6.2:
    webpack-virtual-modules: public
  which@2.0.2:
    which: public
  widest-line@5.0.0:
    widest-line: public
  word-wrap@1.2.5:
    word-wrap: public
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: public
  wrap-ansi@9.0.0:
    wrap-ansi: public
  write-file-atomic@5.0.1:
    write-file-atomic: public
  xml-name-validator@4.0.0:
    xml-name-validator: public
  y18n@5.0.8:
    y18n: public
  yallist@3.1.1:
    yallist: public
  yaml@2.7.1:
    yaml: public
  yargs-parser@21.1.1:
    yargs-parser: public
  yargs@17.7.2:
    yargs: public
  yocto-queue@0.1.0:
    yocto-queue: public
  zrender@5.6.1:
    zrender: public
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.2
pendingBuilds: []
prunedAt: Fri, 04 Jul 2025 07:12:44 GMT
publicHoistPattern:
  - '*'
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmmirror.com/
skipped:
  - '@esbuild/aix-ppc64@0.24.2'
  - '@esbuild/aix-ppc64@0.25.3'
  - '@esbuild/android-arm64@0.24.2'
  - '@esbuild/android-arm64@0.25.3'
  - '@esbuild/android-arm@0.24.2'
  - '@esbuild/android-arm@0.25.3'
  - '@esbuild/android-x64@0.24.2'
  - '@esbuild/android-x64@0.25.3'
  - '@esbuild/darwin-arm64@0.24.2'
  - '@esbuild/darwin-arm64@0.25.3'
  - '@esbuild/darwin-x64@0.24.2'
  - '@esbuild/darwin-x64@0.25.3'
  - '@esbuild/freebsd-arm64@0.24.2'
  - '@esbuild/freebsd-arm64@0.25.3'
  - '@esbuild/freebsd-x64@0.24.2'
  - '@esbuild/freebsd-x64@0.25.3'
  - '@esbuild/linux-arm64@0.24.2'
  - '@esbuild/linux-arm64@0.25.3'
  - '@esbuild/linux-arm@0.24.2'
  - '@esbuild/linux-arm@0.25.3'
  - '@esbuild/linux-ia32@0.24.2'
  - '@esbuild/linux-ia32@0.25.3'
  - '@esbuild/linux-loong64@0.24.2'
  - '@esbuild/linux-loong64@0.25.3'
  - '@esbuild/linux-mips64el@0.24.2'
  - '@esbuild/linux-mips64el@0.25.3'
  - '@esbuild/linux-ppc64@0.24.2'
  - '@esbuild/linux-ppc64@0.25.3'
  - '@esbuild/linux-riscv64@0.24.2'
  - '@esbuild/linux-riscv64@0.25.3'
  - '@esbuild/linux-s390x@0.24.2'
  - '@esbuild/linux-s390x@0.25.3'
  - '@esbuild/linux-x64@0.24.2'
  - '@esbuild/linux-x64@0.25.3'
  - '@esbuild/netbsd-arm64@0.24.2'
  - '@esbuild/netbsd-arm64@0.25.3'
  - '@esbuild/netbsd-x64@0.24.2'
  - '@esbuild/netbsd-x64@0.25.3'
  - '@esbuild/openbsd-arm64@0.24.2'
  - '@esbuild/openbsd-arm64@0.25.3'
  - '@esbuild/openbsd-x64@0.24.2'
  - '@esbuild/openbsd-x64@0.25.3'
  - '@esbuild/sunos-x64@0.24.2'
  - '@esbuild/sunos-x64@0.25.3'
  - '@esbuild/win32-arm64@0.24.2'
  - '@esbuild/win32-arm64@0.25.3'
  - '@esbuild/win32-ia32@0.24.2'
  - '@esbuild/win32-ia32@0.25.3'
  - '@napi-rs/canvas-android-arm64@0.1.73'
  - '@napi-rs/canvas-darwin-arm64@0.1.73'
  - '@napi-rs/canvas-darwin-x64@0.1.73'
  - '@napi-rs/canvas-linux-arm-gnueabihf@0.1.73'
  - '@napi-rs/canvas-linux-arm64-gnu@0.1.73'
  - '@napi-rs/canvas-linux-arm64-musl@0.1.73'
  - '@napi-rs/canvas-linux-riscv64-gnu@0.1.73'
  - '@napi-rs/canvas-linux-x64-gnu@0.1.73'
  - '@napi-rs/canvas-linux-x64-musl@0.1.73'
  - '@parcel/watcher-android-arm64@2.5.1'
  - '@parcel/watcher-darwin-arm64@2.5.1'
  - '@parcel/watcher-darwin-x64@2.5.1'
  - '@parcel/watcher-freebsd-x64@2.5.1'
  - '@parcel/watcher-linux-arm-glibc@2.5.1'
  - '@parcel/watcher-linux-arm-musl@2.5.1'
  - '@parcel/watcher-linux-arm64-glibc@2.5.1'
  - '@parcel/watcher-linux-arm64-musl@2.5.1'
  - '@parcel/watcher-linux-x64-glibc@2.5.1'
  - '@parcel/watcher-linux-x64-musl@2.5.1'
  - '@parcel/watcher-win32-arm64@2.5.1'
  - '@parcel/watcher-win32-ia32@2.5.1'
  - '@rollup/rollup-android-arm-eabi@4.40.0'
  - '@rollup/rollup-android-arm64@4.40.0'
  - '@rollup/rollup-darwin-arm64@4.40.0'
  - '@rollup/rollup-darwin-x64@4.40.0'
  - '@rollup/rollup-freebsd-arm64@4.40.0'
  - '@rollup/rollup-freebsd-x64@4.40.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.40.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.40.0'
  - '@rollup/rollup-linux-arm64-gnu@4.40.0'
  - '@rollup/rollup-linux-arm64-musl@4.40.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.40.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.40.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.40.0'
  - '@rollup/rollup-linux-riscv64-musl@4.40.0'
  - '@rollup/rollup-linux-s390x-gnu@4.40.0'
  - '@rollup/rollup-linux-x64-gnu@4.40.0'
  - '@rollup/rollup-linux-x64-musl@4.40.0'
  - '@rollup/rollup-win32-arm64-msvc@4.40.0'
  - '@rollup/rollup-win32-ia32-msvc@4.40.0'
  - '@tailwindcss/oxide-android-arm64@4.1.4'
  - '@tailwindcss/oxide-darwin-arm64@4.1.4'
  - '@tailwindcss/oxide-darwin-x64@4.1.4'
  - '@tailwindcss/oxide-freebsd-x64@4.1.4'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.4'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.4'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.4'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.4'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.4'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.4'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.4'
  - fsevents@2.3.3
  - lightningcss-darwin-arm64@1.29.2
  - lightningcss-darwin-x64@1.29.2
  - lightningcss-freebsd-x64@1.29.2
  - lightningcss-linux-arm-gnueabihf@1.29.2
  - lightningcss-linux-arm64-gnu@1.29.2
  - lightningcss-linux-arm64-musl@1.29.2
  - lightningcss-linux-x64-gnu@1.29.2
  - lightningcss-linux-x64-musl@1.29.2
  - lightningcss-win32-arm64-msvc@1.29.2
storeDir: D:\.pnpm-store\v10
virtualStoreDir: D:\work\easy-zhipin-pc\houtaipc\yzp-admin\node_modules\.pnpm
virtualStoreDirMaxLength: 60
