export default {
  path: "/coupon_info",
  redirect: "/user/403",
  meta: {
    icon: "ri-user-fill",
    // showLink: false,
    title: "道具管理",
    rank: 13
  },
  children: [
    {
      path: "/coupon_info/coupons",
      meta: {
        title: "道具管理"
      },
      children: [
        {
          path: "/coupon_info/coupons/coupon_manage",
          name: "couponManage",
          component: () => import("@/views/coupons/coupons_manage/index.vue"),
          meta: {
            title: "优惠卷管理"
          }
        },
        {
          path: "/coupon_info/coupons/item_cards",
          name: "itemCards",
          component: () => import("@/views/coupons/Item_cards/index.vue"),
          meta: {
            title: "道具卡管理"
          }
        }
      ]
    }
  ]
} satisfies RouteConfigsTable;
