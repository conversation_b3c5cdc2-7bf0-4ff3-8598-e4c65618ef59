<template>
  <div class="tree-component">
    <!-- 搜索框 -->
    <div v-if="showSearch" class="search-container">
      <el-input
        v-model="filterText"
        :placeholder="searchPlaceholder"
        clearable
        :prefix-icon="Search"
        size="default"
        @input="handleFilter"
      />
    </div>

    <!-- 树形结构 -->
    <div class="tree-container">
      <el-tree
        ref="treeRef"
        :data="treeData"
        :props="treeProps"
        :filter-node-method="filterNode"
        :expand-on-click-node="expandOnClickNode"
        :highlight-current="highlightCurrent"
        :default-expand-all="defaultExpandAll"
        node-key="id"
        @node-click="handleNodeClick"
        @node-expand="handleNodeExpand"
        @node-collapse="handleNodeCollapse"
      >
        <template #default="{ node }">
          <span class="tree-node">
            <span class="tree-node-label">{{ node.label }}</span>
          </span>
        </template>
      </el-tree>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from "vue";
import type { ElTree } from "element-plus";
import type { TreeNode, TreeComponentProps, TreeComponentEmits } from "./types";
import { Search } from "@element-plus/icons-vue";

const props = withDefaults(defineProps<TreeComponentProps>(), {
  showSearch: true,
  searchPlaceholder: "搜索",
  props: () => ({
    children: "children",
    label: "label",
    disabled: "disabled"
  }),
  defaultExpandAll: false,
  expandOnClickNode: false,
  highlightCurrent: true
});

const emit = defineEmits<TreeComponentEmits>();

const treeRef = ref<InstanceType<typeof ElTree>>();
const filterText = ref("");

const treeProps = {
  children: props.props?.children || "children",
  label: props.props?.label || "label",
  disabled: props.props?.disabled || "disabled"
};

// 过滤节点
const filterNode = (value: string, data: TreeNode) => {
  if (!value) return true;
  return data.label.toLowerCase().includes(value.toLowerCase());
};

// 处理搜索
const handleFilter = () => {
  treeRef.value?.filter(filterText.value);
};

// 处理节点点击
const handleNodeClick = (data: TreeNode, node: any, instance: any) => {
  emit("node-click", data, node, instance);
};

// 处理节点展开
const handleNodeExpand = (data: TreeNode, node: any, instance: any) => {
  emit("node-expand", data, node, instance);
};

// 处理节点收起
const handleNodeCollapse = (data: TreeNode, node: any, instance: any) => {
  emit("node-collapse", data, node, instance);
};

// 监听搜索文本变化
watch(filterText, val => {
  nextTick(() => {
    treeRef.value?.filter(val);
  });
});

// 暴露方法
defineExpose({
  filter: (value: string) => {
    filterText.value = value;
    handleFilter();
  },
  setCurrentKey: (key: string | number) => {
    treeRef.value?.setCurrentKey(key);
  },
  getCurrentKey: () => {
    return treeRef.value?.getCurrentKey();
  },
  getCurrentNode: () => {
    return treeRef.value?.getCurrentNode();
  },
  setCheckedKeys: (keys: (string | number)[]) => {
    treeRef.value?.setCheckedKeys(keys);
  },
  getCheckedKeys: () => {
    return treeRef.value?.getCheckedKeys() || [];
  },
  setCheckedNodes: (nodes: TreeNode[]) => {
    treeRef.value?.setCheckedNodes(nodes as any);
  },
  getCheckedNodes: () => {
    return treeRef.value?.getCheckedNodes() || [];
  }
});
</script>

<style scoped>
.tree-component {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-container {
  padding: 16px;
  border-bottom: 1px solid #ebeef5;
}

.tree-container {
  flex: 1;
  padding: 8px;
  overflow-y: auto;
}

.tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.tree-node-label {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:deep(.el-tree-node__content) {
  height: 36px;
  line-height: 36px;
}

:deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #e6f7ff;
  color: #1890ff;
}
</style>
