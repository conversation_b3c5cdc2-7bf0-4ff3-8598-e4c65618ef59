<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import TableDetails from "./components/index.vue";
import TreeComponent from "@/components/TreeComponent/index.vue";
import { getCouponsList, deleteCoupons } from "@/api/coupons/index";
import { Plus } from "@element-plus/icons-vue";
import { ElMessageBox, ElMessage } from "element-plus";

import { cityList } from "@/api/city";

const form = ref<any>({});
const detailsDialogVisible = ref(false);
const isRightType = ref("");
const loading = ref(false);
const isType = ref("");

// 树形数据
const treeData = ref<any>([]);

let formQuery = [
  {
    type: "input",
    key: "provinceName",
    label: "省份名称"
  },
  {
    type: "input",
    key: "cityName",
    label: "城市名称"
  },
  {
    type: "input",
    key: "categoryName",
    label: "道具卡名称"
  }
];
interface Cms {
  property: string;
  label: string;
  width: string;
}
const tableCms: Cms[] = [
  {
    property: "provinceName",
    label: "省份名称",
    width: ""
  },
  {
    property: "cityName",
    label: "城市名称",
    width: ""
  },
  {
    property: "categoryName",
    label: "道具卡名称",
    width: ""
  },
  {
    property: "originalPrice",
    label: "原价",
    width: ""
  },
  {
    property: "price",
    label: "实际售价",
    width: ""
  }
];
const tableData = ref<any[]>([]);

const currentPage = ref(1);
const pageSize = ref(10);
const size = ref<any>(1);
const background = ref(false);
const disabled = ref(false);
const total = ref(0);
const currentRow = ref<any>({});

const optionsConfig = ref<any[]>([
  {
    label: "待审核",
    value: 0
  },
  {
    label: "通过",
    value: 1
  },
  {
    label: "驳回",
    value: 2
  }
]);

// 分页接口入参
const params = ref({
  entity: {
    provinceName: "",
    provinceCode: "",
    cityName: "",
    cityCode: "",
    categoryName: "",
    categoryCode: ""
  },
  orderBy: {},
  page: 1,
  size: 10
});

const handleSizeChange = val => {
  pageSize.value = val;
  params.value.size = val;
  params.value.page = 1;
  getCouponsListData();
};
const handleCurrentChange = val => {
  currentPage.value = val;
  params.value.page = val;
  getCouponsListData();
};

const headerHeight: number = 420;
const tableHeight = ref(window.innerHeight - headerHeight); // 计算高度，headerHeight是你页面顶部栏的高度
function resizeTableHeight() {
  tableHeight.value = window.innerHeight - headerHeight; // 更新表格高度
}

// 新增
const handleAdd = () => {
  currentRow.value = {};
  detailsDialogVisible.value = true;
  isType.value = "isAdd";
};

// 详情
const handleDetails = item => {
  currentRow.value = item.row;
  detailsDialogVisible.value = true;
  isType.value = "isDetails";
};

// 删除
const handleDelete = item => {
  ElMessageBox.confirm("是否确认删除该条数据", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(async () => {
    const res: any = await deleteCoupons({ id: item.row.id });
    if (res.code === 0) {
      ElMessage.success("操作成功");
      getCouponsListData();
    } else {
      ElMessage.error(res.message);
    }
  });
};

// 编辑
const handleEdit = item => {
  currentRow.value = item.row;
  detailsDialogVisible.value = true;
  isType.value = "isEdit";
};

const handleCancelBtn = () => {
  isRightType.value = "";
  detailsDialogVisible.value = false;
  getCouponsListData();
};

// 处理树节点点击
const handleTreeNodeClick = (data: any, node: any, instance: any) => {
  if (data && data?.level === 1) {
    params.value.entity.provinceCode = data.code;
    params.value.entity.provinceName = data.label;
  } else if (data && data?.level === 2) {
    params.value.entity.cityCode = data.code;
    params.value.entity.cityName = data.label;
  }
  getCouponsListData();
};

// 搜索
const handleSearch = () => {
  params.value.entity.provinceName = form.value.provinceName;
  params.value.entity.cityName = form.value.cityName;
  params.value.entity.categoryName = form.value.categoryName;
  // status 不变
  getCouponsListData();
};
// 重置
const handleReset = () => {
  form.value = {};
  params.value = {
    entity: {
      provinceName: "",
      provinceCode: "",
      cityName: "",
      cityCode: "",
      categoryName: "",
      categoryCode: ""
    },
    orderBy: {},
    page: 1,
    size: 10
  };
  getCouponsListData();
};

const getCouponsListData = async () => {
  loading.value = true;
  try {
    const res: any = await getCouponsList(params.value);
    if (res.code === 0) {
      total.value = res.data.total;
      tableData.value = res.data.list;
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};

const deepCloneAndFilter = data => {
  // 深拷贝
  const cloned = JSON.parse(JSON.stringify(data));

  // 递归过滤 level === 3
  function filterLevel3(arr) {
    return arr
      .filter(item => item.level !== 3)
      .map(item => {
        if (item.childList && item.childList.length) {
          item.childList = filterLevel3(item.childList);
        }
        return item;
      });
  }

  return filterLevel3(cloned);
};

// 转换数据格式以适配TreeComponent
function transformTreeData(data) {
  return data.map(item => ({
    id: item.code,
    label: item.name,
    code: item.code,
    level: item.level,
    children: item.childList ? transformTreeData(item.childList) : []
  }));
}

onMounted(async () => {
  const filteredData = await deepCloneAndFilter(cityList);
  treeData.value = transformTreeData(filteredData);
  getCouponsListData();
  console.log("🚀 ~ onMounted ~ treeData.value:", treeData.value);
  window.addEventListener("resize", resizeTableHeight);
});
onUnmounted(() => {
  window.removeEventListener("resize", resizeTableHeight);
});
</script>

<template>
  <div class="page-container">
    <!-- 左侧树形组件 -->
    <div class="left-tree-container">
      <div class="tree-scroll-area">
        <TreeComponent
          :tree-data="treeData"
          :show-search="true"
          search-placeholder="搜索省份/城市"
          @node-click="handleTreeNodeClick"
        />
      </div>
    </div>
    <!-- 右侧内容区域 -->
    <div class="right-content-container">
      <el-card shadow="never">
        <div class="table-header-flex">
          <el-form :inline="true" :model="form" class="table-header-form">
            <el-form-item
              v-for="(item, index) in formQuery"
              :key="index"
              :label="item.label"
              class="form-item"
            >
              <el-input
                v-if="item.type === 'input'"
                v-model="form[item.key]"
                :placeholder="'请输入' + item.label"
                style="width: 180px"
                clearable
              />
              <el-select
                v-else-if="item.type === 'select'"
                v-model="form[item.key]"
                :placeholder="'请选择' + item.label"
                style="width: 180px"
                clearable
              >
                <el-option
                  v-for="option in optionsConfig"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
              <el-date-picker
                v-else-if="item.type === 'datetime'"
                v-model="form[item.key]"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                style="width: 380px"
                value-format="YYYY-MM-DD HH:mm:ss"
                clearable
              />
            </el-form-item>
          </el-form>
          <div class="form-btns">
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button
              type="info"
              style="background-color: #b4c4d1; color: #ffffff; border: #b4c4d1"
              @click="handleReset"
              >重置</el-button
            >
          </div>
        </div>
      </el-card>

      <el-card shadow="never" style="margin-top: 15px; padding: 5px 15px">
        <div class="table-container">
          <el-button
            type="primary"
            :icon="Plus"
            class="add-btn"
            @click="handleAdd"
            >新增</el-button
          >
        </div>
        <el-table
          ref="tableContainer"
          :data="tableData"
          :loading="loading"
          element-loading-text="数据加载中..."
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(255, 255, 255, 0.9)"
          element-loading-svg-view-box="-10, -10, 50, 50"
          style="width: 100%"
          :height="tableHeight"
        >
          <!-- <el-table-column type="selection" width="60" /> -->
          <el-table-column
            v-for="(config, index) in tableCms"
            :key="index"
            :width="config.width"
            :label="config.label"
            show-overflow-tooltip
          >
            <template #default="scope">
              <span v-if="config.property === 'originalPrice'">
                {{ `${scope.row[config.property] / 100} 元` }}
              </span>

              <span v-else-if="config.property === 'price'">
                {{ `${scope.row[config.property] / 100} 元` }}
              </span>

              <span v-else>
                {{ scope.row[config.property] }}
              </span>
            </template>
          </el-table-column>

          <el-table-column fixed="right" label="操作" min-width="120">
            <template #default="scope">
              <el-button
                link
                type="primary"
                style="color: #279efb"
                @click="handleDetails(scope)"
              >
                详情
              </el-button>
              <el-button
                link
                type="primary"
                style="color: #67c23a"
                @click="handleEdit(scope)"
              >
                编辑
              </el-button>
              <el-button
                link
                type="primary"
                style="color: #fb5451"
                @click="handleDelete(scope)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div style="display: flex; justify-content: flex-end; margin-top: 20px">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :size="size"
            :disabled="disabled"
            :background="background"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
      <TableDetails
        v-model:dialogVisible="detailsDialogVisible"
        :currentRow="currentRow"
        :isType="isType"
        @cancelBtn="handleCancelBtn"
      />
    </div>
  </div>
</template>
<style scoped lang="scss">
.billsplit-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 60px;
  line-height: 60px;
}
.table-header {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 5px 20px;
  font-size: 14px;
  button {
    width: 74px !important;
    height: 40px !important;
  }
}
.table-header-row {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  gap: 24px;
  background: #fff;
}
.form-item {
  display: flex;
  align-items: center;
  gap: 8px;
}
.table-header-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}
.table-header-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  flex: 1;
}
.form-item {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}
.form-btns {
  display: flex;
  align-items: center;
  margin-left: 24px;
  gap: 12px;
}

/* 页面布局样式 */
.page-container {
  display: flex;
  align-items: stretch;
  // height: calc(100vh - 84px); // 84px为顶部导航栏高度
  gap: 16px;
}

.left-tree-container {
  width: 280px;
  min-width: 280px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  max-height: 81vh;
}

.tree-scroll-area {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  /* 让树内容撑满并滚动 */
}

.right-content-container {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.table-container {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  .add-btn {
    margin-right: 10px;
    color: #fff;
  }
}
</style>
